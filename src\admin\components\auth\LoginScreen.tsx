import React, { useState } from 'react';

import {
    Email as Email<PERSON>con,
    LockOutlined as Lock<PERSON><PERSON>,
    Lock as PasswordIcon,
    Visibility,
    VisibilityOff
} from '@mui/icons-material';
import {
    Alert,
    Avatar,
    Box,
    Button,
    Card,
    CardContent,
    Checkbox,
    CircularProgress,
    Container,
    Divider,
    FormControlLabel,
    IconButton,
    InputAdornment,
    Link,
    Paper,
    Tab,
    Tabs,
    TextField,
    Typography
} from '@mui/material';
import { useAuth } from '@shared/hooks/useAuth';
import { useNavigate } from '@tanstack/react-router';

const LoginScreen: React.FC = () => {
    const navigate = useNavigate();
    const { login, loading, error } = useAuth();
    const [tabIndex, setTabIndex] = useState(0);

    const [formData, setFormData] = useState({
        email: '',
        password: '',
        rememberMe: false
    });

    const [showPassword, setShowPassword] = useState(false);
    const [formErrors, setFormErrors] = useState<{
        email?: string;
        password?: string;
    }>({});

    const validateForm = () => {
        const errors: { email?: string; password?: string } = {};

        if (!formData.email) {
            errors.email = 'Email is required';
        } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
            errors.email = 'Email is invalid';
        }

        if (!formData.password) {
            errors.password = 'Password is required';
        } else if (formData.password.length < 6) {
            errors.password = 'Password must be at least 6 characters';
        }

        setFormErrors(errors);
        return Object.keys(errors).length === 0;
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!validateForm()) {
            return;
        }

        const result = await login({
            email: formData.email,
            password: formData.password,
            rememberMe: formData.rememberMe
        });

        if (result.success) {
            navigate({ to: '/dashboard' });
        }
    };

    const handleInputChange =
        (field: keyof typeof formData) => (e: React.ChangeEvent<HTMLInputElement>) => {
            const value = field === 'rememberMe' ? e.target.checked : e.target.value;
            setFormData(prev => ({ ...prev, [field]: value }));

            // Clear field error when user starts typing
            if (formErrors[field as keyof typeof formErrors]) {
                setFormErrors(prev => ({ ...prev, [field]: undefined }));
            }
        };

    return (
        <Box
            sx={{
                minHeight: '100vh',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: '#f5f6fa',
                p: 2
            }}
        >
            <Card sx={{ width: '100%', maxWidth: 420, borderRadius: 3, boxShadow: 4 }}>
                <CardContent sx={{ p: 4 }}>
                    <Box sx={{ textAlign: 'center', mb: 3 }}>
                        <img src="/misa-logo.png" alt="MISA" height={40} />
                        <Typography variant="h6" sx={{ mt: 2 }}>
                            Đăng nhập
                        </Typography>
                    </Box>

                    <Tabs value={tabIndex} onChange={(e, newIndex) => setTabIndex(newIndex)} centered>
                        <Tab label="Với mật khẩu" />
                        <Tab label="Với mã QR" />
                    </Tabs>

                    {tabIndex === 0 ? (
                        <Box component="form" onSubmit={handleSubmit} sx={{ mt: 2 }}>
                            {error && (
                                <Alert severity="error" sx={{ mb: 2 }}>
                                    {error}
                                </Alert>
                            )}
                            <TextField
                                label="Số điện thoại/email"
                                fullWidth
                                margin="normal"
                                value={formData.email}
                                onChange={handleInputChange('email')}
                                InputProps={{
                                    startAdornment: (
                                        <InputAdornment position="start">
                                            <EmailIcon />
                                        </InputAdornment>
                                    )
                                }}
                            />
                            <TextField
                                label="Mật khẩu"
                                fullWidth
                                margin="normal"
                                type={showPassword ? 'text' : 'password'}
                                value={formData.password}
                                onChange={handleInputChange('password')}
                                InputProps={{
                                    startAdornment: (
                                        <InputAdornment position="start">
                                            <PasswordIcon />
                                        </InputAdornment>
                                    ),
                                    endAdornment: (
                                        <InputAdornment position="end">
                                            <IconButton onClick={() => setShowPassword(!showPassword)}>
                                                {showPassword ? <VisibilityOff /> : <Visibility />}
                                            </IconButton>
                                        </InputAdornment>
                                    )
                                }}
                            />
                            <FormControlLabel
                                control={
                                    <Checkbox
                                        checked={formData.rememberMe}
                                        onChange={handleInputChange('rememberMe')}
                                    />
                                }
                                label="Ghi nhớ đăng nhập"
                                sx={{ mt: 1 }}
                            />
                            <Button
                                type="submit"
                                fullWidth
                                variant="contained"
                                color="primary"
                                disabled={loading}
                                sx={{ mt: 3, mb: 2, py: 1.5, fontWeight: 600 }}
                            >
                                {loading ? <CircularProgress size={24} color="inherit" /> : 'Đăng nhập'}
                            </Button>

                            <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1 }}>
                                <Link href="#" underline="hover">
                                    Quên mật khẩu?
                                </Link>
                                <Link href="#" underline="hover">
                                    Đăng ký
                                </Link>
                            </Box>
                        </Box>
                    ) : (
                        <Box sx={{ textAlign: 'center', mt: 4 }}>
                            <Typography variant="body2" color="text.secondary">
                                Quét mã QR bằng ứng dụng MISA để đăng nhập
                            </Typography>
                            <Box
                                sx={{
                                    width: 150,
                                    height: 150,
                                    mx: 'auto',
                                    mt: 2,
                                    border: '1px solid #ccc',
                                    borderRadius: 2,
                                    background: '#eee'
                                }}
                            />
                        </Box>
                    )}

                    <Divider sx={{ my: 3 }}>Hoặc đăng nhập với</Divider>

                    <Box sx={{ display: 'flex', justifyContent: 'center', gap: 2 }}>
                        <IconButton>
                            <img src="/google-icon.svg" alt="Google" width={24} />
                        </IconButton>
                        <IconButton>
                            <img src="/apple-icon.svg" alt="Apple" width={24} />
                        </IconButton>
                        <IconButton>
                            <img src="/microsoft-icon.svg" alt="Microsoft" width={24} />
                        </IconButton>
                    </Box>
                </CardContent>
            </Card>
        </Box>
    );
};

export default LoginScreen;
